# JavaScript 基础

## 什么是JavaScript？

JavaScript是一种高级的、解释型的编程语言。它是Web开发的核心技术之一。

## 变量

在JavaScript中，可以使用var、let或const来声明变量：

```javascript
var name = "张三";
let age = 25;
const PI = 3.14159;
```

## 函数

函数是可重复使用的代码块：

```javascript
function greet(name) {
    return "你好，" + name + "！";
}

// 调用函数
console.log(greet("世界"));
```

## 对象

对象是键值对的集合：

```javascript
const person = {
    name: "李四",
    age: 30,
    city: "北京"
};
```
